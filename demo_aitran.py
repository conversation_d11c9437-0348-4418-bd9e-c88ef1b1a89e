#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Demo script để phân tích file AITran.f bằng deAnalyst
Script này cho phép người dùng chạy phân tích nhanh
"""

import os
import sys
import time
import logging
from pathlib import Path

# Import các module từ dự án deAnalyst
try:
    from model import FileAnalyzer
    print("✅ deAnalyst modules loaded successfully")
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Đảm bảo bạn đang chạy script từ thư mục chứa model.py")
    sys.exit(1)

def demo_analyze_aitran():
    """Demo phân tích file AITran.f"""
    
    print("🚀 DEMO PHÂN TÍCH FILE AITran.f")
    print("=" * 50)
    
    # Đường dẫn file AITran.f
    aitran_path = r"E:\FastBusinessOnline\App_Data\Controllers\Dir\AITran.f"
    
    # Kiểm tra file tồn tại
    if not os.path.exists(aitran_path):
        print(f"❌ File không tồn tại: {aitran_path}")
        print("📝 Vui lòng cập nhật đường dẫn file trong script")
        return False
    
    print(f"📁 File: {aitran_path}")
    print(f"📏 Size: {os.path.getsize(aitran_path):,} bytes")
    
    # Khởi tạo FileAnalyzer
    analyzer = FileAnalyzer()
    
    # Phân tích file
    print("\n🔍 Đang phân tích...")
    start_time = time.time()
    
    try:
        result = analyzer.analyze_file(aitran_path)
        end_time = time.time()
        
        processing_time = end_time - start_time
        print(f"⏱️  Thời gian: {processing_time:.3f}s")
        
        # Hiển thị kết quả
        status = result.get('status', 'unknown')
        print(f"\n📊 Kết quả: {status.upper()}")
        
        if status == 'success':
            print("✅ PHÂN TÍCH THÀNH CÔNG!")
            
            # Thông tin cơ bản
            root_tag = result.get('root_tag_name', 'N/A')
            root_attrs = result.get('root_attributes', {})
            title_attrs = result.get('title_attributes', {})
            
            print(f"\n🏗️  Cấu trúc:")
            print(f"   • Thẻ gốc: <{root_tag}>")
            print(f"   • Loại: {root_attrs.get('type', 'N/A')}")
            print(f"   • Bảng: {root_attrs.get('table', 'N/A')}")
            print(f"   • ID: {root_attrs.get('id', 'N/A')}")
            
            print(f"\n📝 Tiêu đề:")
            print(f"   • VN: {title_attrs.get('v', 'N/A')}")
            print(f"   • EN: {title_attrs.get('e', 'N/A')}")
            
            # Thống kê fields
            fields_by_category = result.get('root_fields_by_category', {})
            total_fields = sum(len(fields) for fields in fields_by_category.values())
            
            print(f"\n📋 Fields:")
            print(f"   • Tổng: {total_fields} fields")
            print(f"   • Categories: {len(fields_by_category)}")
            
            # Top categories
            sorted_categories = sorted(fields_by_category.items(), 
                                      key=lambda x: len(x[1]), reverse=True)
            
            print(f"\n🏆 Top Categories:")
            for i, (cat_name, fields) in enumerate(sorted_categories[:3]):
                print(f"   {i+1}. {cat_name}: {len(fields)} fields")
            
            # Một số fields quan trọng
            chung_fields = fields_by_category.get('Chung', [])
            if chung_fields:
                print(f"\n📋 Một số fields quan trọng:")
                for field in chung_fields[:5]:
                    name = field.get('name', 'N/A')
                    header_v = field.get('_header_attrs', {}).get('v', '')
                    if header_v:
                        print(f"   • {name}: {header_v}")
                    else:
                        print(f"   • {name}")
            
            return True
            
        else:
            print("❌ PHÂN TÍCH THẤT BẠI")
            message = result.get('message', 'Không có thông báo')
            print(f"   Lỗi: {message}")
            return False
            
    except Exception as e:
        end_time = time.time()
        processing_time = end_time - start_time
        
        print(f"❌ EXCEPTION sau {processing_time:.3f}s:")
        print(f"   Lỗi: {str(e)}")
        return False

def show_help():
    """Hiển thị hướng dẫn sử dụng"""
    
    print("📖 HƯỚNG DẪN SỬ DỤNG deAnalyst")
    print("=" * 40)
    print()
    print("🎯 Mục đích:")
    print("   Tool phân tích file ERP (.f) để trích xuất cấu trúc và metadata")
    print()
    print("🚀 Cách chạy:")
    print("   python demo_aitran.py")
    print()
    print("📁 File được phân tích:")
    print("   E:\\FastBusinessOnline\\App_Data\\Controllers\\Dir\\AITran.f")
    print()
    print("📊 Kết quả:")
    print("   • Cấu trúc XML (thẻ gốc, thuộc tính)")
    print("   • Danh sách fields và categories")
    print("   • Thông tin forms, views")
    print("   • Metadata chi tiết")
    print()
    print("🔧 Yêu cầu:")
    print("   • Python 3.7+")
    print("   • lxml library")
    print("   • File model.py trong cùng thư mục")

def main():
    """Hàm chính"""
    
    print("🔍 deAnalyst - ERP Development Analyst Tool")
    print("=" * 60)
    print("📅 Demo Date:", time.strftime("%Y-%m-%d %H:%M:%S"))
    print()
    
    # Kiểm tra tham số dòng lệnh
    if len(sys.argv) > 1 and sys.argv[1] in ['-h', '--help', 'help']:
        show_help()
        return
    
    # Chạy demo
    success = demo_analyze_aitran()
    
    print(f"\n🎯 KẾT LUẬN:")
    if success:
        print("✅ Demo chạy thành công!")
        print("🚀 Tool deAnalyst hoạt động tốt với file AITran.f")
    else:
        print("❌ Demo gặp lỗi!")
        print("🔧 Vui lòng kiểm tra đường dẫn file và dependencies")
    
    print(f"\n📖 Để xem hướng dẫn chi tiết:")
    print("   python demo_aitran.py --help")

if __name__ == "__main__":
    # Tắt logging để output sạch hơn
    logging.getLogger().setLevel(logging.WARNING)
    
    main()
