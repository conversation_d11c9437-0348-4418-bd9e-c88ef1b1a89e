# model.py
import sqlite3
import pyodbc
import os
import shutil
from lxml import etree
import configparser
from pathlib import Path
import re
from io import BytesIO
import logging

# <PERSON><PERSON><PERSON> <PERSON>ình hệ thống logging cơ bản
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')


# --- <PERSON><PERSON><PERSON> vụ <PERSON> h<PERSON>nh và Quản lý D<PERSON> án (giữ nguyên) ---
class ConfigService:
    def __init__(self, config_file='config.ini'):
        self.config_file = config_file
        self.config = configparser.ConfigParser(interpolation=None)
        if not os.path.exists(self.config_file):
            self._create_default_config()
        self.config.read(self.config_file)
    def _create_default_config(self):
        self.config['Projects'] = {}
        with open(self.config_file, 'w') as f:
            self.config.write(f)
    def get_projects(self):
        return list(self.config['Projects'].keys()) if 'Projects' in self.config else []
    def get_project_details(self, project_name):
        if 'Projects' not in self.config or project_name not in self.config['Projects']: return None
        project_path = self.config['Projects'][project_name]
        project_config_path = os.path.join(project_path, 'project.ini')
        if not os.path.exists(project_config_path): return None
        try:
            proj_config = configparser.ConfigParser(interpolation=None)
            proj_config.read(project_config_path)
            details = {
                'name': project_name,
                'description': proj_config.get('Details', 'Description', fallback=''),
                'source_path': proj_config.get('Paths', 'SourcePath', fallback=''),
                'sys_db_conn': proj_config.get('Database', 'SysDB', fallback=''),
                'app_db_conn': proj_config.get('Database', 'AppDB', fallback=''),
                'project_dir': project_path
            }
            return details
        except configparser.Error: return None
    def save_project(self, details):
        project_name = details['name']
        project_dir = os.path.join('projects', project_name)
        os.makedirs(project_dir, exist_ok=True)
        if 'Projects' not in self.config: self.config.add_section('Projects')
        self.config['Projects'][project_name] = project_dir
        with open(self.config_file, 'w') as f: self.config.write(f)
        proj_config = configparser.ConfigParser(interpolation=None)
        proj_config['Details'] = {'Description': details.get('description', '')}
        proj_config['Paths'] = {'SourcePath': details.get('source_path', '')}
        proj_config['Database'] = {'SysDB': details.get('sys_db_conn', ''), 'AppDB': details.get('app_db_conn', '')}
        with open(os.path.join(project_dir, 'project.ini'), 'w') as f: proj_config.write(f)
    def delete_project(self, project_name):
        if 'Projects' not in self.config or project_name not in self.config['Projects']: raise ValueError(f"Dự án '{project_name}' không tồn tại.")
        project_dir = self.config['Projects'][project_name]
        del self.config['Projects'][project_name]
        with open(self.config_file, 'w') as f: self.config.write(f)
        if os.path.isdir(project_dir): shutil.rmtree(project_dir)

# --- Dịch vụ Cơ sở dữ liệu (giữ nguyên) ---
class DatabaseService:
    def __init__(self): self.connection = None
    def _parse_conn_string(self, conn_str):
        params = {}
        for part in conn_str.split(';'):
            if '=' in part:
                key, value = part.split('=', 1)
                params[key.strip().lower()] = value.strip()
        return params
    def connect(self, connection_string):
        if not connection_string: return False, "Chuỗi kết nối trống."
        params = self._parse_conn_string(connection_string)
        server = params.get('data source') or params.get('server')
        if not server: return False, f"Chuỗi kết nối không chứa 'Data Source' hoặc 'SERVER'."
        database = params.get('initial catalog') or params.get('database')
        uid = params.get('user id') or params.get('uid')
        pwd = params.get('password') or params.get('pwd')
        clean_parts = [f"SERVER={server}"]
        if database: clean_parts.append(f"DATABASE={database}")
        if uid: clean_parts.append(f"UID={uid}")
        if pwd: clean_parts.append(f"PWD={pwd}")
        trusted = params.get('trusted_connection', 'no').lower()
        integrated_security = params.get('integrated security', '').lower()
        if trusted in ('yes', 'true') or integrated_security == 'sspi': clean_parts.append("Trusted_Connection=yes")
        base_conn_str = ";".join(clean_parts)
        common_drivers = ['ODBC Driver 17 for SQL Server', 'ODBC Driver 13 for SQL Server', 'SQL Server Native Client 11.0', 'SQL Server']
        last_error = None
        for driver in common_drivers:
            try:
                conn_str_with_driver = f'DRIVER={{{driver}}};{base_conn_str}'
                self.connection = pyodbc.connect(conn_str_with_driver, timeout=5)
                return True, self.connection
            except pyodbc.Error as e:
                last_error = e
                continue
        error_message = f"Không thể kết nối CSDL.\nĐã thử các driver: {', '.join(common_drivers)}.\nLỗi cuối cùng: {last_error}"
        self.connection = None
        return False, error_message
    def get_db_objects(self, conn):
        try:
            with conn.cursor() as cursor:
                tables = [row.TABLE_NAME for row in cursor.execute("SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' ORDER BY TABLE_NAME").fetchall()]
                views = [row.TABLE_NAME for row in cursor.execute("SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'VIEW' ORDER BY TABLE_NAME").fetchall()]
                sps = [row.ROUTINE_NAME for row in cursor.execute("SELECT ROUTINE_NAME FROM INFORMATION_SCHEMA.ROUTINES WHERE ROUTINE_TYPE = 'PROCEDURE' ORDER BY ROUTINE_NAME").fetchall()]
                return {'Tables': tables, 'Views': views, 'Stored Procedures': sps}
        except Exception as e:
            logging.error(f"Lỗi khi lấy đối tượng CSDL: {e}")
            return {}
    def get_table_details(self, conn, table_name):
        columns = []
        try:
            with conn.cursor() as cursor:
                for row in cursor.columns(table=table_name):
                    columns.append({'name': row.column_name, 'type': row.type_name, 'size': row.column_size, 'nullable': 'YES' if row.is_nullable == 'YES' else 'NO'})
        except Exception as e: logging.error(f"Lỗi khi lấy chi tiết bảng {table_name}: {e}")
        return columns
    def get_object_definition(self, conn, object_name):
        try:
            with conn.cursor() as cursor:
                cursor.execute(f"sp_helptext '{object_name}'")
                return ''.join([row[0] for row in cursor.fetchall()])
        except Exception as e: return f"Không thể lấy định nghĩa cho {object_name}: {e}"

# --- Dịch vụ Phân tích File ---

class SimpleFileResolver(etree.Resolver):
    def __init__(self, base_dir):
        self.base_dir = base_dir

    def resolve(self, system_url, public_id, context):
        abs_path = os.path.abspath(os.path.join(self.base_dir, system_url))
        if os.path.exists(abs_path):
            logging.info(f"Resolver: Đã tìm thấy file entity tại {abs_path}")
            return self.resolve_filename(abs_path, context)
        logging.warning(f"Resolver: Không tìm thấy file entity: {abs_path}")
        return None

class FileAnalyzer:
    def __init__(self):
        self.entity_field_map = {}
        self.main_file_fields = set()
        self.main_file_path = None

    def scan_web_config(self, source_path):
        web_config_path = Path(source_path) / 'web.config'
        if not web_config_path.exists(): return None, "Không tìm thấy file web.config"
        try:
            tree = etree.parse(str(web_config_path))
            conn_strings = tree.xpath('/configuration/connectionStrings/add')
            sys_db, app_dbs = '', []
            for conn in conn_strings:
                name = conn.get('name', '').lower()
                conn_str = conn.get('connectionString')
                if 'sys' in name: sys_db = conn_str
                else: app_dbs.append(conn_str)
            return sys_db, (app_dbs[0] if app_dbs else '')
        except Exception as e: return None, f"Lỗi khi đọc web.config: {e}"

    def _scan_and_map_entities(self, content_to_scan, path_of_content_source, full_doctype_context):
        entity_def_pattern = re.compile(r'<!ENTITY(.*?%)?([^>]+)>')
        for is_param, entity_body in entity_def_pattern.findall(content_to_scan):
            entity_body = entity_body.strip()
            
            if 'SYSTEM' in entity_body:
                path_match = re.search(r'SYSTEM\s+"([^"]+)"', entity_body)
                if path_match:
                    rel_path = path_match.group(1)
                    current_base_dir = os.path.dirname(path_of_content_source)
                    abs_path = os.path.abspath(os.path.join(current_base_dir, rel_path))
                    if os.path.exists(abs_path):
                        try:
                            with open(abs_path, 'r', encoding='utf-8-sig') as f:
                                nested_content = f.read().lstrip('\ufeff')
                            self._scan_and_map_entities(nested_content, abs_path, full_doctype_context)
                        except Exception as e:
                            logging.error(f"Lỗi khi đọc hoặc quét file entity {abs_path}: {e}")
            else:
                inline_match = re.search(r'([\w\.]+)\s+"(.*?)"', entity_body, re.DOTALL)
                if inline_match:
                    entity_name, entity_content = inline_match.groups()
                    entity_name = entity_name.strip()
                    try:
                        temp_xml_string = f'<!DOCTYPE r [{full_doctype_context}]><r>{entity_content}</r>'
                        
                        parser = etree.XMLParser(load_dtd=True, no_network=True, recover=True, resolve_entities=True)
                        original_base_dir = os.path.dirname(self.main_file_path)
                        parser.resolvers.add(SimpleFileResolver(original_base_dir))

                        dummy_root = etree.fromstring(temp_xml_string.encode('utf-8'), parser)
                        
                        for field_node in dummy_root.xpath('.//*[local-name()="field"]'):
                            field_name = field_node.get('name') or field_node.get('Name')
                            if field_name and field_name not in self.main_file_fields:
                                if field_name not in self.entity_field_map:
                                    self.entity_field_map[field_name] = path_of_content_source
                                    logging.info(f"Mapped inline field '{field_name}' to source '{path_of_content_source}'")
                    except Exception as e:
                        logging.warning(f"Không thể phân tích nội dung entity nội tuyến '{entity_name}' từ '{path_of_content_source}': {e}")

    def _process_field_elements(self, field_elements, xpath_ns_agnostic):
        fields_by_category = {}
        for el in field_elements:
            field_data = dict(el.attrib)
            field_name = field_data.get('name') or field_data.get('Name')
            
            if field_name:
                if field_name in self.entity_field_map:
                    field_data['_source_entity'] = self.entity_field_map[field_name]
                
                header_el_list = xpath_ns_agnostic(el, 'header')
                if header_el_list: field_data['_header_attrs'] = dict(header_el_list[0].attrib)
                
                footer_el_list = xpath_ns_agnostic(el, 'footer')
                if footer_el_list: field_data['_footer_attrs'] = dict(footer_el_list[0].attrib)
                
                items_el_list = xpath_ns_agnostic(el, 'items')
                if items_el_list:
                    items_el = items_el_list[0]
                    items_data = dict(items_el.attrib)
                    child_items_list = xpath_ns_agnostic(items_el, 'item')
                    if child_items_list:
                        items_data['_child_items'] = []
                        for item_child_el in child_items_list:
                            item_child_data = dict(item_child_el.attrib)
                            text_el_list = xpath_ns_agnostic(item_child_el, 'text')
                            if text_el_list: item_child_data['_text_attrs'] = dict(text_el_list[0].attrib)
                            items_data['_child_items'].append(item_child_data)
                    field_data['_items_attrs'] = items_data
                
                handle_el_list = xpath_ns_agnostic(el, 'handle')
                if handle_el_list: field_data['_handle_attrs'] = dict(handle_el_list[0].attrib)

                items_attrs = field_data.get('_items_attrs', {})
                footer_attrs = field_data.get('_footer_attrs', {})

                if items_attrs.get('style') == 'Mask' and footer_attrs and not items_attrs.get('_child_items'):
                    v_footer = footer_attrs.get('v', '')
                    e_footer = footer_attrs.get('e', '')
                    v_map = {}
                    if v_footer:
                        v_parts = [p.strip() for p in v_footer.split(',')]
                        for part in v_parts:
                            # (ĐÃ SỬA) Regex chỉ lấy key, giữ lại toàn bộ chuỗi cho value
                            match = re.match(r'(\S+)\s*-.*', part)
                            if match:
                                val = match.group(1).strip()
                                v_map[val] = part
                    e_map = {}
                    if e_footer:
                        e_parts = [p.strip() for p in e_footer.split(',')]
                        for part in e_parts:
                            match = re.match(r'(\S+)\s*-.*', part)
                            if match:
                                val = match.group(1).strip()
                                e_map[val] = part
                                
                    all_keys = sorted(list(set(v_map.keys()) | set(e_map.keys())))
                    child_items = []
                    for key in all_keys:
                        item_data = { 'value': key, '_text_attrs': { 'v': v_map.get(key, ''), 'e': e_map.get(key, '') } }
                        child_items.append(item_data)
                    
                    if child_items:
                        items_attrs['_child_items'] = child_items
                        field_data['_items_attrs'] = items_attrs

                category_index = field_data.get('categoryIndex', 'Chung')
                fields_by_category.setdefault(category_index, []).append(field_data)
        return fields_by_category

    def analyze_file(self, file_path):
        logging.info(f"--- Bắt đầu phân tích file: {file_path} ---")
        self.entity_field_map = {}
        self.main_file_fields = set()
        self.main_file_path = file_path
        original_content = ""
        base_dir = os.path.dirname(file_path)

        try:
            with open(file_path, 'r', encoding='utf-8-sig') as f:
                original_content = f.read()
            logging.info("Đã đọc file thành công.")

            try:
                logging.info("Quét các field được định nghĩa trực tiếp trong file chính...")
                parser_no_entities = etree.XMLParser(resolve_entities=False)
                tree_no_entities = etree.parse(BytesIO(original_content.encode('utf-8')), parser_no_entities)
                for field_node in tree_no_entities.xpath('.//*[local-name()="field"]'):
                    field_name = field_node.get('name') or field_node.get('Name')
                    if field_name:
                        self.main_file_fields.add(field_name)
                logging.info(f"Các field trong file chính: {self.main_file_fields}")
            except Exception as e:
                logging.error(f"Lỗi khi quét các field của file chính: {e}")

            logging.info("Bắt đầu quét trước đệ quy để map nguồn của field...")
            doctype_pattern = re.compile(r'<!DOCTYPE\s+[\w]+\s*\[(.*?)\]>', re.DOTALL)
            doctype_match = doctype_pattern.search(original_content)
            if doctype_match:
                doctype_content = doctype_match.group(1) or ""
                self._scan_and_map_entities(doctype_content, file_path, doctype_content)

            analysis_result = {
                "status": "success", "root_tag_name": "", "root_attributes": {},
                "title_attributes": {}, "partition_attributes": {},
                "root_fields_by_category": {},
                "forms": [],
                "views": [], "categories": [],
                "content": original_content
            }

            try:
                logging.info("Bắt đầu phân tích cấu trúc XML chính bằng lxml...")
                parser = etree.XMLParser(load_dtd=True, no_network=True, recover=True, resolve_entities=True)
                parser.resolvers.add(SimpleFileResolver(base_dir))
                
                tree = etree.parse(BytesIO(original_content.encode('utf-8')), parser)
                root = tree.getroot()
                logging.info(f"Phân tích XML thành công. Thẻ gốc: <{root.tag}>")
                
                analysis_result['root_tag_name'] = root.tag.split('}')[-1]
                analysis_result['root_attributes'] = dict(root.attrib)
                
                def xpath_ns_agnostic(element, query):
                    parts = query.split('/')
                    new_parts = []
                    for part in parts:
                        if part and part != '.':
                            if not part.startswith('@') and not part.endswith('()'):
                                new_parts.append(f"*[local-name()='{part}']")
                            else:
                                new_parts.append(part)
                        else:
                            new_parts.append(part)
                    return element.xpath('/'.join(new_parts))

                title_el_list = xpath_ns_agnostic(root, './title')
                if title_el_list: analysis_result['title_attributes'] = dict(title_el_list[0].attrib)

                partition_el_list = xpath_ns_agnostic(root, './partition')
                if partition_el_list: analysis_result['partition_attributes'] = dict(partition_el_list[0].attrib)

                logging.info("Bắt đầu trích xuất các field chung...")
                root_field_elements = xpath_ns_agnostic(root, './fields/field')
                analysis_result['root_fields_by_category'] = self._process_field_elements(root_field_elements, xpath_ns_agnostic)
                logging.info(f"Đã trích xuất {len(root_field_elements)} field chung.")

                logging.info("Bắt đầu trích xuất các forms...")
                form_elements = xpath_ns_agnostic(root, './forms/form')
                forms_data = []
                for form_el in form_elements:
                    form_data = {
                        'attributes': dict(form_el.attrib),
                        'header': {},
                        'fields_by_category': {}
                    }
                    header_el = xpath_ns_agnostic(form_el, './header')
                    if header_el:
                        form_data['header'] = dict(header_el[0].attrib)
                    
                    form_field_elements = xpath_ns_agnostic(form_el, './fields/field')
                    form_data['fields_by_category'] = self._process_field_elements(form_field_elements, xpath_ns_agnostic)
                    forms_data.append(form_data)
                
                analysis_result['forms'] = forms_data
                logging.info(f"Đã trích xuất {len(forms_data)} form.")
                
                logging.info("Bắt đầu trích xuất dữ liệu <view> và <category>...")
                for view_el in xpath_ns_agnostic(root, './/view'):
                    analysis_result["views"].append(dict(view_el.attrib))
                for cat_el in xpath_ns_agnostic(root, './/category'):
                    cat_attrs = dict(cat_el.attrib)
                    header_el_list = xpath_ns_agnostic(cat_el, 'header')
                    if header_el_list: cat_attrs['_header_attrs'] = dict(header_el_list[0].attrib)
                    analysis_result["categories"].append(cat_attrs)
                logging.info(f"Đã trích xuất {len(analysis_result['views'])} <view> và {len(analysis_result['categories'])} <category>.")

            except etree.XMLSyntaxError as e:
                logging.error(f"LỖI CÚ PHÁP XML: {e}", exc_info=False)
                analysis_result["status"] = "error"
                analysis_result["message"] = f"Lỗi cú pháp XML: {e}"
                return analysis_result

            logging.info(f"--- Hoàn thành phân tích file: {file_path} ---")
            return analysis_result

        except Exception as e:
            logging.error(f"Lỗi không xác định trong quá trình phân tích file: {e}", exc_info=True)
            return {"status": "error", "message": str(e), "content": original_content}
