# view.py
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import ttkbootstrap as bstrap
from ttkbootstrap.constants import *
import os
import subprocess
import sys

class MainAppView(bstrap.Window):
    """Lớp giao diện ch<PERSON>h của <PERSON>ng dụng."""
    def __init__(self, title, theme='superhero'):
        super().__init__(themename=theme)
        self.title(title)
        self.geometry("1600x900") # Tăng chiều rộng để có thêm không gian
        self.controller = None
        self._create_widgets()
        self.update_idletasks()
        width, height = self.winfo_width(), self.winfo_height()
        x = (self.winfo_screenwidth() // 2) - (width // 2)
        y = (self.winfo_screenheight() // 2) - (height // 2)
        self.geometry(f'{width}x{height}+{x}+{y}')

    def set_controller(self, controller):
        self.controller = controller

    def _create_widgets(self):
        self.menu_bar = tk.Menu(self)
        self.config(menu=self.menu_bar)
        
        project_menu = tk.Menu(self.menu_bar, tearoff=0)
        self.menu_bar.add_cascade(label="Dự án", menu=project_menu)
        project_menu.add_command(label="Dự án mới...", command=lambda: self.show_project_dialog())
        project_menu.add_command(label="Quản lý dự án...", command=self.show_project_manager)
        project_menu.add_separator()
        project_menu.add_command(label="Thoát", command=self.quit)

        main_pane = ttk.PanedWindow(self, orient=HORIZONTAL)
        main_pane.pack(fill=BOTH, expand=True, padx=5, pady=5)

        # --- Left Pane ---
        left_frame = ttk.Frame(main_pane, padding=5)
        
        proj_select_frame = ttk.Frame(left_frame)
        proj_select_frame.pack(fill=X, pady=(0, 5))
        ttk.Label(proj_select_frame, text="Dự án hiện tại:").pack(side=LEFT, padx=(0, 5))
        self.project_var = tk.StringVar()
        self.project_combo = ttk.Combobox(proj_select_frame, textvariable=self.project_var, state="readonly")
        self.project_combo.pack(side=LEFT, fill=X, expand=True)
        self.project_combo.bind("<<ComboboxSelected>>", self.on_project_selected)

        self.left_notebook = ttk.Notebook(left_frame)
        self.left_notebook.pack(fill=BOTH, expand=True)

        # Tab 1: Source Code
        tab1 = ttk.Frame(self.left_notebook)
        self.left_notebook.add(tab1, text="Mã nguồn")
        self.file_search_var = tk.StringVar()
        file_search_entry = ttk.Entry(tab1, textvariable=self.file_search_var)
        file_search_entry.pack(fill=X, padx=2, pady=2)
        file_search_entry.bind("<KeyRelease>", self._on_search_files)
        self.file_tree = ttk.Treeview(tab1, show="tree headings")
        self.file_tree.heading("#0", text="Tên File / Thư mục")
        self.file_tree.pack(fill=BOTH, expand=True)
        self.file_tree.bind("<<TreeviewSelect>>", self.on_file_selected)
        self.file_tree.bind("<Button-3>", self._show_file_tree_context_menu)


        # Tab 2: Database
        tab2 = ttk.Frame(self.left_notebook)
        self.left_notebook.add(tab2, text="Cơ sở dữ liệu")
        self.db_search_var = tk.StringVar()
        db_search_entry = ttk.Entry(tab2, textvariable=self.db_search_var)
        db_search_entry.pack(fill=X, padx=2, pady=2)
        db_search_entry.bind("<KeyRelease>", self._on_search_db)
        self.db_tree = ttk.Treeview(tab2)
        self.db_tree.heading("#0", text="Đối tượng CSDL")
        self.db_tree.pack(fill=BOTH, expand=True)
        self.db_tree.bind("<<TreeviewSelect>>", self.on_db_object_selected)

        main_pane.add(left_frame, weight=2)

        # --- Right Pane with Tabs ---
        right_frame = ttk.Frame(main_pane, padding=5)
        self.right_notebook = ttk.Notebook(right_frame)
        self.right_notebook.pack(fill=BOTH, expand=True)

        # Tab 1: Phân tích chi tiết (Treeview)
        analysis_tab = ttk.Frame(self.right_notebook)
        self.right_notebook.add(analysis_tab, text="Phân tích chi tiết")
        # (ĐÃ SỬA) Thêm cột 'Type'
        self.analysis_tree = ttk.Treeview(analysis_tab, columns=("V", "E", "Type", "Value", "Source"))
        self.analysis_tree.heading("#0", text="Tên trường")
        self.analysis_tree.heading("V", text="V")
        self.analysis_tree.heading("E", text="E")
        self.analysis_tree.heading("Type", text="Type")
        self.analysis_tree.heading("Value", text="Giá trị")
        self.analysis_tree.heading("Source", text="Nguồn")
        self.analysis_tree.column("#0", width=200)
        self.analysis_tree.column("V", width=150)
        self.analysis_tree.column("E", width=150)
        self.analysis_tree.column("Type", width=100)
        self.analysis_tree.column("Value", width=150)
        self.analysis_tree.column("Source", width=150)
        self.analysis_tree.pack(fill=BOTH, expand=True)
        
        style = bstrap.Style()
        try:
            warning_color = style.colors.warning
            primary_color = style.colors.primary
        except AttributeError:
            warning_color = 'orange' 
            primary_color = 'blue'
        self.analysis_tree.tag_configure('has_source', foreground=warning_color)
        self.analysis_tree.tag_configure('category_group', foreground=primary_color)
        self.analysis_tree.bind("<Button-3>", self._show_analysis_context_menu)

        # Tab 2: Báo cáo tóm tắt
        report_tab = ttk.Frame(self.right_notebook)
        self.right_notebook.add(report_tab, text="Báo cáo tóm tắt")
        self.report_viewer = bstrap.ScrolledText(report_tab, wrap="word")
        self.report_viewer.pack(fill=BOTH, expand=True)
        self.report_viewer.config(state='disabled')

        # Tab 3: Nội dung gốc
        content_tab = ttk.Frame(self.right_notebook)
        self.right_notebook.add(content_tab, text="Nội dung gốc")
        self.code_viewer = bstrap.ScrolledText(content_tab, wrap="none")
        self.code_viewer.pack(fill=BOTH, expand=True)
        self.code_viewer.config(state='disabled')

        main_pane.add(right_frame, weight=5)

    def _show_file_tree_context_menu(self, event):
        item_id = self.file_tree.identify_row(event.y)
        if not item_id:
            return

        self.file_tree.selection_set(item_id)
        item_values = self.file_tree.item(item_id, "values")
        
        context_menu = tk.Menu(self, tearoff=0)
        path = None

        if item_values and item_values[0]:
            path = item_values[0]
            if os.path.isfile(path):
                context_menu.add_command(label="Mở file", 
                                         command=lambda p=path: self._open_source_file(p))
                context_menu.add_command(label="Mở thư mục chứa file", 
                                         command=lambda p=path: self._open_source_folder(p))
        else:
            if self.controller and self.controller.current_project_details:
                path_parts = [self.file_tree.item(item_id, "text")]
                parent_id = self.file_tree.parent(item_id)
                while parent_id:
                    path_parts.insert(0, self.file_tree.item(parent_id, "text"))
                    parent_id = self.file_tree.parent(parent_id)
                
                base_path = self.controller.current_project_details['source_path']
                path = os.path.join(base_path, *path_parts)
                
                if os.path.isdir(path):
                     context_menu.add_command(label="Mở thư mục", 
                                             command=lambda p=path: self._open_source_file(p))

        if path and context_menu.index('end') is not None:
            context_menu.post(event.x_root, event.y_root)


    def _show_analysis_context_menu(self, event):
        item_id = self.analysis_tree.identify_row(event.y)
        if not item_id:
            return

        self.analysis_tree.selection_set(item_id)
        item_values = self.analysis_tree.item(item_id, "values")
        
        # (ĐÃ SỬA) Cập nhật index của cột Source
        if len(item_values) > 4 and item_values[4]:
            source_path = item_values[4]
            
            context_menu = tk.Menu(self, tearoff=0)
            context_menu.add_command(label="Mở file nguồn", 
                                     command=lambda p=source_path: self._open_source_file(p))
            context_menu.add_command(label="Mở thư mục chứa file", 
                                     command=lambda p=source_path: self._open_source_folder(p))
            
            context_menu.post(event.x_root, event.y_root)

    def _open_source_file(self, path):
        try:
            if os.path.exists(path):
                if sys.platform == "win32":
                    os.startfile(os.path.normpath(path))
                elif sys.platform == "darwin":
                    subprocess.call(["open", path])
                else:
                    subprocess.call(["xdg-open", path])
            else:
                self.show_error("Lỗi", f"Không tìm thấy đường dẫn:\n{path}")
        except Exception as e:
            self.show_error("Lỗi", f"Không thể mở đường dẫn:\n{e}")

    def _open_source_folder(self, path):
        try:
            if os.path.isfile(path):
                folder = os.path.dirname(path)
                if sys.platform == "win32":
                    subprocess.run(['explorer', '/select,', os.path.normpath(path)])
                else:
                    self._open_source_file(folder)
            else:
                self.show_error("Lỗi", f"Đường dẫn không phải là file:\n{path}")
        except Exception as e:
            self.show_error("Lỗi", f"Không thể mở thư mục:\n{e}")

    def _on_search_files(self, event=None):
        if self.controller: self.controller.search_files(self.file_search_var.get())
    def _on_search_db(self, event=None):
        if self.controller: self.controller.search_db_objects(self.db_search_var.get())

    def on_project_selected(self, event=None):
        self.file_search_var.set("")
        self.db_search_var.set("")
        if self.controller: self.controller.load_project(self.project_var.get())

    def on_file_selected(self, event=None):
        if self.controller:
            selected_item = self.file_tree.focus()
            if selected_item:
                file_path = self.file_tree.item(selected_item, "values")
                if file_path and isinstance(file_path, (list, tuple)) and file_path[0]:
                    self.controller.analyze_file(file_path[0])
                    self.right_notebook.select(0)

    def on_db_object_selected(self, event=None):
        if self.controller:
            selected_item = self.db_tree.focus()
            if selected_item and self.db_tree.parent(selected_item):
                parent_id = self.db_tree.parent(selected_item)
                object_type = self.db_tree.item(parent_id, "text")
                object_name = self.db_tree.item(selected_item, "text")
                self.controller.show_db_object_details(object_type, object_name)
                self.right_notebook.select(2)

    def update_project_list(self, projects):
        current_project = self.project_var.get()
        self.project_combo['values'] = projects
        if current_project in projects: self.project_var.set(current_project)
        elif projects: self.project_var.set(projects[0])
        else: self.project_var.set('')
        self.on_project_selected()

    def update_file_tree(self, tree_structure, open_all=False):
        self.clear_file_tree()
        self._populate_file_tree(self.file_tree, "", tree_structure, open_all)

    def _populate_file_tree(self, tree, parent_id, node, open_all):
        for name, value in sorted(node.items()):
            if isinstance(value, dict):
                folder_id = tree.insert(parent_id, "end", text=name, values=('',), open=open_all)
                self._populate_file_tree(tree, folder_id, value, open_all)
            else:
                tree.insert(parent_id, "end", text=name, values=(value,))

    def update_db_tree(self, db_objects):
        self.clear_db_tree()
        for obj_type, obj_list in db_objects.items():
            parent_id = self.db_tree.insert("", "end", text=obj_type, open=True)
            for obj_name in sorted(obj_list):
                self.db_tree.insert(parent_id, "end", text=obj_name)
    
    def _display_grouped_fields(self, parent_id, fields_by_category, categories_info):
        if not fields_by_category:
            return

        def sort_key(x):
            if x == 'Chung': return -1
            try: return int(x)
            except (ValueError, TypeError): return float('inf')

        sorted_categories = sorted(fields_by_category.keys(), key=sort_key)

        for category_index in sorted_categories:
            fields_in_category = fields_by_category[category_index]
            
            category_info = categories_info.get(str(category_index))
            category_display_name = f"Category[{category_index}]"
            v_name_cat, e_name_cat = "", ""
            
            if category_info:
                header = category_info.get('_header_attrs', {})
                v_name_cat = header.get('v', '')
                e_name_cat = header.get('e', '')
            elif category_index == 'Chung':
                category_display_name = 'Thông tin chung'

            cat_node_id = self.analysis_tree.insert(parent_id, "end", 
                                                    text=category_display_name, 
                                                    values=(v_name_cat, e_name_cat, "", "", ""),
                                                    open=True, 
                                                    tags=('category_group',))

            for field_attrs in fields_in_category:
                attrs_copy = field_attrs.copy()
                field_name = attrs_copy.pop('name', None) or attrs_copy.pop('Name', 'Không tên')
                
                source_entity_path = attrs_copy.pop('_source_entity', None)
                header_attrs = attrs_copy.pop('_header_attrs', None)
                items_attrs = attrs_copy.pop('_items_attrs', None)
                handle_attrs = attrs_copy.pop('_handle_attrs', None)
                
                # (ĐÃ SỬA) Lấy thêm thông tin type
                field_type = attrs_copy.pop('type', '')

                v_name = header_attrs.get('v', '') if header_attrs else ''
                e_name = header_attrs.get('e', '') if header_attrs else ''
                
                tags_to_apply = ('has_source',) if source_entity_path else ()
                source_display = os.path.basename(source_entity_path) if source_entity_path else ""
                
                # (ĐÃ SỬA) Thêm field_type vào tuple values
                field_id = self.analysis_tree.insert(cat_node_id, "end", text=str(field_name), 
                                                     values=(v_name, e_name, field_type, "", source_entity_path),
                                                     open=False, tags=tags_to_apply)
                self.analysis_tree.set(field_id, "Source", source_display)

                for key, value in sorted(attrs_copy.items()):
                    if value is not None: self.analysis_tree.insert(field_id, "end", text=key, values=("", "", "", str(value), ""))
                
                if header_attrs:
                    for key, value in sorted(header_attrs.items()):
                        if key not in ['v', 'e']:
                            self.analysis_tree.insert(field_id, "end", text=f"header.{key}", values=("", "", "", str(value if value is not None else ''), ""))

                if items_attrs:
                    child_items = items_attrs.pop('_child_items', None)
                    for key, value in sorted(items_attrs.items()):
                        self.analysis_tree.insert(field_id, "end", text=f"items.{key}", values=("", "", "", str(value if value is not None else ''), ""))

                    if child_items:
                        for i, item_data in enumerate(child_items):
                            item_attrs_copy = item_data.copy()
                            text_attrs = item_attrs_copy.pop('_text_attrs', None)
                            item_value = item_attrs_copy.pop('value', '')
                            v_name_item, e_name_item = '', ''
                            if text_attrs:
                                v_name_item = text_attrs.pop('v', '')
                                e_name_item = text_attrs.pop('e', '')

                            item_id = self.analysis_tree.insert(field_id, "end", 
                                                          text=f"items.item[{i}]", 
                                                          values=(v_name_item, e_name_item, "", item_value, ""))

                            for key, value in sorted(item_attrs_copy.items()):
                                self.analysis_tree.insert(item_id, "end", text=key, values=("", "", "", str(value if value is not None else ''), ""))
                            
                            if text_attrs:
                                for key, value in sorted(text_attrs.items()):
                                    self.analysis_tree.insert(item_id, "end", text=f"text.{key}", values=("", "", "", str(value if value is not None else ''), ""))
                
                if handle_attrs:
                    for key, value in sorted(handle_attrs.items()):
                        self.analysis_tree.insert(field_id, "end", text=f"handle.{key}", values=("", "", "", str(value if value is not None else ''), ""))

    def update_analysis_tree(self, result):
        self.clear_analysis_tree()
        if result['status'] != 'success': return

        # (ĐÃ SỬA) Cập nhật tuple giá trị rỗng
        empty_values = ("", "", "", "", "")

        root_tag = result.get('root_tag_name')
        root_attrs = result.get('root_attributes')
        if root_tag and root_attrs is not None:
            title_attrs = result.get('title_attributes', {})
            v_title = title_attrs.pop('v', '')
            e_title = title_attrs.pop('e', '')
            
            # (ĐÃ SỬA) Cập nhật tuple values
            root_values = (v_title, e_title, "", "", "")
            root_id = self.analysis_tree.insert("", "end", text=f"Thẻ gốc: <{root_tag}>", open=True, values=root_values)

            for key, value in sorted(root_attrs.items()):
                self.analysis_tree.insert(root_id, "end", text=key, values=("", "", "", str(value), ""))
            
            for key, value in sorted(title_attrs.items()):
                 self.analysis_tree.insert(root_id, "end", text=f"title.{key}", values=("", "", "", str(value), ""))
            
            partition_attrs = result.get('partition_attributes')
            if partition_attrs:
                for key, value in sorted(partition_attrs.items()):
                    self.analysis_tree.insert(root_id, "end", text=f"partition.{key}", values=("", "", "", str(value), ""))

        categories_info = {cat.get('index'): cat for cat in result.get('categories', [])}

        forms = result.get('forms', [])
        if forms:
            forms_root_id = self.analysis_tree.insert("", "end", text="Forms", open=True, values=empty_values)
            for form_data in forms:
                form_attrs = form_data.get('attributes', {})
                form_header = form_data.get('header', {})
                form_id = form_attrs.get('id', 'N/A')
                v_name = form_header.get('v', '')
                e_name = form_header.get('e', '')
                
                form_node_id = self.analysis_tree.insert(forms_root_id, "end", 
                                                         text=f"Form [{form_id}]",
                                                         values=(v_name, e_name, "", "", ""),
                                                         open=True)
                self._display_grouped_fields(form_node_id, form_data.get('fields_by_category', {}), categories_info)

        root_fields = result.get('root_fields_by_category', {})
        if root_fields:
            root_fields_id = self.analysis_tree.insert("", "end", text="Trường chung", open=True, values=empty_values)
            self._display_grouped_fields(root_fields_id, root_fields, categories_info)

        views = result.get('views', [])
        if views:
            views_id = self.analysis_tree.insert("", "end", text="Views", open=True, values=empty_values)
            for view_attrs in views:
                view_name = view_attrs.pop('id', None) or view_attrs.pop('ID', 'Không ID')
                view_node_id = self.analysis_tree.insert(views_id, "end", text=str(view_name), open=False, values=empty_values)
                for key, value in sorted(view_attrs.items()):
                    self.analysis_tree.insert(view_node_id, "end", text=key, values=("", "", "", str(value if value is not None else ''), ""))

        categories = result.get('categories', [])
        if categories:
            cat_id = self.analysis_tree.insert("", "end", text="Categories", open=True, values=empty_values)
            for cat_attrs in categories:
                attrs_copy = cat_attrs.copy()
                header_attrs = attrs_copy.pop('_header_attrs', None)
                node_text = attrs_copy.pop('index', 'Không có Index')
                cat_node_id = self.analysis_tree.insert(cat_id, "end", text=str(node_text), open=False, values=empty_values)
                for key, value in sorted(attrs_copy.items()):
                    if value is not None:
                        self.analysis_tree.insert(cat_node_id, "end", text=key, values=("", "", "", str(value), ""))
                if header_attrs:
                    for key, value in sorted(header_attrs.items()):
                        self.analysis_tree.insert(cat_node_id, "end", text=f"header.{key}", values=("", "", "", str(value if value is not None else ''), ""))

    def display_raw_content(self, content):
        self.code_viewer.config(state='normal'); self.code_viewer.delete('1.0', END)
        self.code_viewer.insert('1.0', content); self.code_viewer.config(state='disabled')
        
    def display_analysis_report(self, report_md):
        self.report_viewer.config(state='normal'); self.report_viewer.delete('1.0', END)
        self.report_viewer.insert('1.0', report_md); self.report_viewer.config(state='disabled')

    def clear_views(self):
        self.clear_file_tree(); self.clear_db_tree(); self.clear_analysis_tree()
        self.display_raw_content(""); self.display_analysis_report("")

    def clear_file_tree(self):
        for i in self.file_tree.get_children(): self.file_tree.delete(i)
    def clear_db_tree(self):
        for i in self.db_tree.get_children(): self.db_tree.delete(i)
    def clear_analysis_tree(self):
        for i in self.analysis_tree.get_children(): self.analysis_tree.delete(i)

    def show_project_dialog(self, project_details=None):
        ProjectDialog(self, self.controller, project_details)
    def show_project_manager(self):
        if self.controller: ProjectManagerDialog(self, self.controller)
    def show_info(self, title, message): messagebox.showinfo(title, message)
    def show_error(self, title, message): messagebox.showerror(title, message)

# --- Các lớp Dialog (giữ nguyên) ---
class ProjectManagerDialog(bstrap.Toplevel):
    def __init__(self, parent, controller):
        super().__init__(parent)
        self.controller = controller; self.transient(parent); self.grab_set()
        self.title("Quản lý dự án"); self.geometry("600x400")
        self._create_widgets(); self._load_projects(); self._center_window()
    def _center_window(self):
        self.withdraw(); self.update_idletasks(); parent = self.master
        x = parent.winfo_x() + (parent.winfo_width() // 2) - (self.winfo_width() // 2)
        y = parent.winfo_y() + (parent.winfo_height() // 2) - (self.winfo_height() // 2)
        self.geometry(f'+{x}+{y}'); self.deiconify()
    def _create_widgets(self):
        main_frame = ttk.Frame(self, padding=10); main_frame.pack(fill=BOTH, expand=True)
        cols = ("name", "path"); self.tree = ttk.Treeview(main_frame, columns=cols, show="headings")
        self.tree.heading("name", text="Tên dự án"); self.tree.heading("path", text="Đường dẫn mã nguồn")
        self.tree.column("name", width=150); self.tree.pack(side=TOP, fill=BOTH, expand=True)
        btn_frame = ttk.Frame(main_frame, padding=(0, 10, 0, 0)); btn_frame.pack(side=BOTTOM, fill=X)
        ttk.Button(btn_frame, text="Đóng", command=self.destroy).pack(side=RIGHT, padx=5)
        ttk.Button(btn_frame, text="Xóa", command=self._on_delete, style=DANGER).pack(side=RIGHT, padx=5)
        ttk.Button(btn_frame, text="Sửa", command=self._on_edit, style=SUCCESS).pack(side=RIGHT, padx=5)
    def _load_projects(self):
        for i in self.tree.get_children(): self.tree.delete(i)
        projects = self.controller.get_all_projects_details()
        for proj in projects: self.tree.insert("", "end", values=(proj['name'], proj['source_path']), iid=proj['name'])
    def _get_selected_project_name(self):
        selected_item = self.tree.focus()
        if not selected_item: messagebox.showwarning("Chưa chọn dự án", "Vui lòng chọn một dự án.", parent=self); return None
        return self.tree.item(selected_item, "values")[0]
    def _on_edit(self):
        project_name = self._get_selected_project_name()
        if project_name:
            details = self.controller.config_service.get_project_details(project_name)
            if details: self.master.show_project_dialog(project_details=details); self.destroy()
    def _on_delete(self):
        project_name = self._get_selected_project_name()
        if project_name:
            if messagebox.askyesno("Xác nhận xóa", f"Bạn có chắc chắn muốn xóa dự án '{project_name}' không?", parent=self):
                if self.controller.delete_project(project_name): self._load_projects()

class ProjectDialog(bstrap.Toplevel):
    def __init__(self, parent, controller, project_details=None):
        super().__init__(parent)
        self.transient(parent); self.grab_set(); self.controller = controller
        title = "Chỉnh sửa dự án" if project_details else "Tạo dự án mới"; self.title(title)
        self._create_form()
        if project_details: self._fill_form(project_details); self.entries['name'].config(state='readonly')
        self._center_window()
    def _center_window(self):
        self.withdraw(); self.update_idletasks(); parent = self.master
        x = parent.winfo_x() + (parent.winfo_width() // 2) - (self.winfo_width() // 2)
        y = parent.winfo_y() + (parent.winfo_height() // 2) - (self.winfo_height() // 2)
        self.geometry(f'+{x}+{y}'); self.deiconify()
    def _create_form(self):
        form_frame = ttk.Frame(self, padding=20); form_frame.pack(fill=BOTH, expand=True)
        fields = {"name": "Tên dự án:", "description": "Mô tả:", "source_path": "Đường dẫn mã nguồn:", "sys_db_conn": "Chuỗi kết nối Sys DB:", "app_db_conn": "Chuỗi kết nối App DB:"}
        self.entries = {}
        for i, (key, label) in enumerate(fields.items()):
            ttk.Label(form_frame, text=label).grid(row=i, column=0, sticky=W, pady=2, padx=5)
            if "path" in key:
                path_frame = ttk.Frame(form_frame); path_frame.grid(row=i, column=1, sticky=EW, pady=2)
                self.entries[key] = ttk.Entry(path_frame); self.entries[key].pack(side=LEFT, fill=X, expand=True)
                ttk.Button(path_frame, text="...", width=3, command=lambda k=key: self._browse_folder(k)).pack(side=LEFT, padx=(5,0))
            else:
                self.entries[key] = ttk.Entry(form_frame); self.entries[key].grid(row=i, column=1, sticky=EW, pady=2)
        form_frame.columnconfigure(1, weight=1)
        auto_btn = ttk.Button(form_frame, text="Lấy thông tin tự động từ web.config", command=self.auto_get_info)
        auto_btn.grid(row=len(fields), column=1, sticky=E, pady=10)
        btn_frame = ttk.Frame(form_frame); btn_frame.grid(row=len(fields)+1, column=0, columnspan=2, pady=(10,0))
        ttk.Button(btn_frame, text="Lưu", command=self.save, style=SUCCESS).pack(side=LEFT, padx=5)
        ttk.Button(btn_frame, text="Hủy", command=self.destroy, style=DANGER).pack(side=LEFT, padx=5)
    def _fill_form(self, project_details):
        for key, widget in self.entries.items(): widget.insert(0, project_details.get(key, ''))
    def _browse_folder(self, key):
        folder = filedialog.askdirectory(parent=self)
        if folder: self.entries[key].delete(0, END); self.entries[key].insert(0, folder)
    def auto_get_info(self):
        source_path = self.entries['source_path'].get()
        if not source_path: messagebox.showwarning("Thiếu thông tin", "Vui lòng cung cấp đường dẫn mã nguồn trước.", parent=self); return
        if self.controller: self.controller.auto_fetch_db_info(source_path, self.update_db_connections)
    def update_db_connections(self, sys_db, app_db):
        if sys_db is not None or app_db is not None:
            self.entries['sys_db_conn'].delete(0, END); self.entries['sys_db_conn'].insert(0, sys_db or '')
            self.entries['app_db_conn'].delete(0, END); self.entries['app_db_conn'].insert(0, app_db or '')
            messagebox.showinfo("Thành công", "Đã tìm thấy và điền thông tin kết nối CSDL.", parent=self)
        else: messagebox.showerror("Lỗi", app_db, parent=self)
    def save(self):
        details = {key: widget.get() for key, widget in self.entries.items()}
        if not details['name'] or not details['source_path']: messagebox.showwarning("Thiếu thông tin", "Tên dự án và đường dẫn mã nguồn là bắt buộc.", parent=self); return
        if self.controller: self.controller.save_project(details)
        self.destroy()
